# pg-queue

基于PostgreSQL的轻量级消息队列库，提供延时消息、优先级队列和重试机制。

## 特性

- **持久化存储**: 使用PostgreSQL数据库存储消息，保证消息不丢失
- **延时消息**: 支持指定延时时间的消息处理，通过定时任务实现（1分钟间隔）
- **优先级队列**: 支持消息优先级，高优先级消息优先处理
- **重试机制**: 支持消息处理失败后的延时重试
- **死信队列**: 支持将处理失败的消息移动到死信队列
- **集群支持**: 可选的PostgreSQL NOTIFY机制，支持集群环境下的消息通知
- **批量操作**: 支持批量发送和批量处理消息
- **Spring集成**: 提供Spring Boot自动配置支持

## 环境要求

- Java 17+
- PostgreSQL 9.5+
- Spring Framework 6.x（可选，用于Spring集成）

## 快速开始

### 1. 添加依赖

```xml
<repositories>
    <repository>
        <id>jitpack.io</id>
        <url>https://jitpack.io</url>
    </repository>
</repositories>

<dependency>
    <groupId>com.github.luckygc</groupId>
    <artifactId>pg-queue</artifactId>
    <version>1.1.0</version>
</dependency>
```

### 2. 创建数据库表

执行 `src/main/resources/ddl.sql` 中的SQL语句创建必要的数据库表：

- `pgmq_pending_queue`: 待处理消息队列
- `pgmq_invisible_queue`: 延时消息队列
- `pgmq_processing_queue`: 处理中消息队列
- `pgmq_dead_queue`: 死信消息队列

### 3. 基础使用

```java
// 创建队列管理器
QueueManager queueManager = new QueueManagerImpl(
    new JdbcTemplate(dataSource),
    new TransactionTemplate(transactionManager)
);

// 使用PgmqManager（推荐方式）
PgmqManager pgmqManager = new PgmqManagerImpl(jdbcTemplate);

// 发送普通消息
pgmqManager.queue().send("order", "{\"orderId\": 123}");

// 发送延时消息（5分钟后处理）
pgmqManager.delayQueue().send("order", "{\"orderId\": 124}", Duration.ofMinutes(5));

// 发送优先级消息
pgmqManager.priorityQueue().send("order", "{\"orderId\": 125}", 10);
```

### 4. 消息处理

```java
// 实现消息处理器
MessageHandler handler = new MessageHandler() {
    @Override
    public String topic() {
        return "order";
    }

    @Override
    public int threadCount() {
        return 8; // 处理线程数，默认为1
    }

    @Override
    public void handle(Message message) {
        try {
            // 处理消息逻辑
            processOrder(message.getPayload());
            message.delete(); // 处理成功，删除消息
        } catch (RetryableException e) {
            // 可重试异常
            if (message.getAttempt() >= 3) {
                message.dead(); // 超过重试次数，进入死信队列
            } else {
                message.retry(Duration.ofMinutes(10)); // 10分钟后重试
            }
        } catch (Exception e) {
            // 不可重试异常，直接进入死信队列
            message.dead();
        }
    }
};

// 注册处理器并启动
PgmqManager pgmqManager = new PgmqManagerImpl(jdbcTemplate);
pgmqManager.registerHandler(handler);
pgmqManager.start();
```

### 5. 手动拉取消息

```java
// 手动拉取单条消息
Message message = pgmqManager.queue().poll("order");
if (message != null) {
    // 处理消息
    processOrder(message.getPayload());
    message.delete();
}
```

## Spring Boot集成

### 1. 启用自动配置

```java
@SpringBootApplication
@EnablePgmq
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### 2. 注入使用

```java
@Service
public class OrderService {

    @Autowired
    private QueueManager queueManager;

    public void createOrder(Order order) {
        // 业务逻辑
        saveOrder(order);

        // 发送消息（自动参与事务）
        queueManager.queue("order").send("order", order.toJson());
    }
}
```

## API参考

### PgmqManager

主要的管理器接口，提供队列操作和生命周期管理。

```java
// 获取普通消息队列
MessageQueue queue();

// 获取延时消息队列
DelayMessageQueue delayQueue();

// 获取优先级消息队列
PriorityMessageQueue priorityQueue();

// 注册消息处理器
void registerHandler(MessageHandler handler);

// 启动管理器
void start() throws SQLException;

// 停止管理器
void stop();
```

### MessageQueue

消息队列接口，提供消息发送和拉取功能。

```java
// 发送消息
void send(String topic, String message);
void send(String topic, List<String> messages);

// 拉取消息
Message poll(String topic);
List<Message> poll(String topic, int maxPoll);
```

### DelayMessageQueue

延时消息队列接口。

```java
// 发送延时消息
void send(String topic, String message, Duration processDelay);
void send(String topic, List<String> messages, Duration processDelay);
```

### PriorityMessageQueue

优先级消息队列接口。

```java
// 发送优先级消息
void send(String topic, String message, int priority);
void send(String topic, List<String> messages, int priority);
```

### Message

消息对象，提供消息处理相关方法。

```java
// 获取消息内容
String getPayload();

// 获取重试次数
int getAttempt();

// 处理成功，删除消息
void delete();

// 重试消息
void retry(Duration retryDelay);

// 移入死信队列
void dead();
```

## 高级配置

### 启用PostgreSQL NOTIFY

启用NOTIFY机制可以在集群环境下实现实时消息通知：

```java
// 创建支持NOTIFY的管理器
PgmqManager pgmqManager = new PgmqManagerImpl(
    jdbcTemplate,
    "*************************************", // 数据库连接URL
    "username",
    "password"
);
```

启用NOTIFY后，当有新消息时，集群中的所有节点都能实时收到通知。

### 批量消息处理

```java
// 批量发送消息
List<String> messages = Arrays.asList("msg1", "msg2", "msg3");
pgmqManager.queue().send("batch-topic", messages);

// 批量发送延时消息
pgmqManager.delayQueue().send("batch-topic", messages, Duration.ofMinutes(5));

// 批量发送优先级消息
pgmqManager.priorityQueue().send("batch-topic", messages, 10);
```

### 消息处理配置

```java
MessageHandler handler = new MessageHandler() {
    @Override
    public String topic() {
        return "order";
    }

    @Override
    public int threadCount() {
        // 处理线程数，范围[1,200]，默认为1
        return Runtime.getRuntime().availableProcessors() * 2;
    }

    @Override
    public int maxPoll() {
        // 每次最大拉取消息数，范围[1,5000]，默认为1
        return 100;
    }

    @Override
    public void handle(Message message) {
        // 处理逻辑
    }
};
```

## 数据库表结构

项目使用5个核心表来管理消息的生命周期：

- `pgmq_pending_queue`: 待处理消息队列
- `pgmq_invisible_queue`: 延时消息队列（未到处理时间）
- `pgmq_processing_queue`: 处理中消息队列（已被消费者拉取）
- `pgmq_dead_queue`: 死信消息队列（处理失败的消息）

## 工作原理

1. **消息发送**: 普通消息直接进入`pending_queue`，延时消息进入`invisible_queue`
2. **定时调度**: 每分钟执行一次定时任务，将到期的延时消息和超时的处理中消息移回`pending_queue`
3. **消息消费**: 消费者从`pending_queue`拉取消息，消息移入`processing_queue`
4. **消息处理**: 处理成功删除消息，处理失败可重试或移入死信队列

## 许可证

Apache License 2.0
